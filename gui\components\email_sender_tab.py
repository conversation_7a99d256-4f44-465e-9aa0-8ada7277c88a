"""
Email Sender Tab for Cold Emailer GUI
"""
import pandas as pd
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QLineEdit, QSpinBox, QCheckBox, QFileDialog, QProgressBar, QTextEdit,
    QGroupBox, QFormLayout, QMessageBox, QSplitter
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import qtawesome as qta

# Import the email sender module
from email_sender import EmailSender
from template_manager import TemplateManager

class EmailSenderWorker(QThread):
    """Worker thread for sending emails"""
    progress_update = pyqtSignal(int, str)
    finished = pyqtSignal(int, int)
    error = pyqtSignal(str)
    
    def __init__(self, email_sender, template_manager, template_id, contacts_file, test_mode, limit):
        super().__init__()
        self.email_sender = email_sender
        self.template_manager = template_manager
        self.template_id = template_id
        self.contacts_file = contacts_file
        self.test_mode = test_mode
        self.limit = limit
    
    def run(self):
        try:
            # Load contacts from file
            if self.contacts_file.endswith('.xlsx') or self.contacts_file.endswith('.xls'):
                df = pd.read_excel(self.contacts_file)
            elif self.contacts_file.endswith('.csv'):
                df = pd.read_csv(self.contacts_file)
            else:
                self.error.emit(f"Unsupported file format: {self.contacts_file}")
                return
            
            # Apply limit if specified
            if self.limit is not None and self.limit > 0:
                df = df.head(self.limit)
            
            total_contacts = len(df)
            successful_emails = 0
            
            # Process each contact
            for index, row in df.iterrows():
                try:
                    # Extract contact information
                    full_name = row.get('Professor Name', '')
                    email = row.get('Professor Email', '')
                    research_area = row.get('Research Area', '')
                    
                    # Skip if email is missing
                    if pd.isna(email) or not email:
                        self.progress_update.emit(index, f"Skipping {full_name} due to missing email")
                        continue
                    
                    # Update progress
                    self.progress_update.emit(index, f"Processing {full_name} at {email}...")
                    
                    # Prepare variables for template
                    variables = {
                        'full_name': full_name,
                        'email': email,
                        'research_area': research_area
                    }
                    
                    # Send email
                    if self.test_mode:
                        # In test mode, just simulate sending
                        self.progress_update.emit(index, f"[TEST] Would send to {full_name} at {email}")
                        successful_emails += 1
                    else:
                        # Get template
                        template = self.template_manager.get_template(self.template_id)
                        if not template:
                            self.error.emit(f"Template '{self.template_id}' not found")
                            return

                        # Personalize template
                        subject = template.get('subject', '')
                        body = template.get('body', '')

                        # Replace variables
                        for var, value in variables.items():
                            subject = subject.replace(f"{{{var}}}", str(value))
                            body = body.replace(f"{{{var}}}", str(value))

                        # Send actual email
                        result = self.email_sender.send_email(
                            to_email=email,
                            subject=subject,
                            body=body
                        )
                        if result['success']:
                            successful_emails += 1
                            self.progress_update.emit(index, f"Successfully sent to {full_name}")
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            self.progress_update.emit(index, f"Failed to send to {full_name}: {error_msg}")
                
                except Exception as e:
                    self.progress_update.emit(index, f"Error processing {email}: {str(e)}")
            
            # Emit finished signal with statistics
            self.finished.emit(successful_emails, total_contacts)
            
        except Exception as e:
            self.error.emit(f"Error sending emails: {str(e)}")

class EmailSenderTab(QWidget):
    """Tab for sending emails"""
    
    def __init__(self):
        super().__init__()
        self.email_sender = EmailSender()
        self.template_manager = TemplateManager()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        # Main layout with proper spacing
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        
        # Create a main splitter for better proportions
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        main_splitter.setChildrenCollapsible(False)
        main_splitter.setHandleWidth(8)
        main_splitter.setStyleSheet("QSplitter::handle { background-color: #e9ecef; }")
        
        # Top section containing config and preview
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(16)
        
        # ===== LEFT SIDE - CONFIGURATION =====
        config_group = QGroupBox("Email Configuration")
        config_group.setStyleSheet("QGroupBox { font-weight: 600; }")
        config_layout = QFormLayout(config_group)
        config_layout.setSpacing(16)
        config_layout.setContentsMargins(16, 24, 16, 16)
        config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        config_layout.setRowWrapPolicy(QFormLayout.RowWrapPolicy.WrapLongRows)
        
        # Template selection
        template_label = QLabel("Email Template:")
        template_label.setStyleSheet("font-weight: 500;")
        
        template_layout = QHBoxLayout()
        template_layout.setSpacing(8)
        
        self.template_combo = QComboBox()
        self.template_combo.setMinimumHeight(36)
        self.template_combo.setStyleSheet(
            "padding: 0 8px; border: 1px solid #ced4da; border-radius: 4px;"
        )
        self.refresh_templates()
        template_layout.addWidget(self.template_combo, 1)  # 1 = stretch factor
        
        self.refresh_button = QPushButton(qta.icon('fa5s.sync'), "")
        self.refresh_button.setToolTip("Refresh Templates")
        self.refresh_button.setFixedSize(36, 36)
        self.refresh_button.setStyleSheet(
            "QPushButton { background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px; }"
            "QPushButton:hover { background-color: #e9ecef; }"
        )
        self.refresh_button.clicked.connect(self.refresh_templates)
        template_layout.addWidget(self.refresh_button)
        
        config_layout.addRow(template_label, template_layout)
        
        # Contacts file selection
        contacts_label = QLabel("Contacts File:")
        contacts_label.setStyleSheet("font-weight: 500;")
        
        contacts_layout = QHBoxLayout()
        contacts_layout.setSpacing(8)
        
        self.contacts_file_edit = QLineEdit()
        self.contacts_file_edit.setPlaceholderText("Select contacts file (Excel/CSV)")
        self.contacts_file_edit.setReadOnly(True)
        self.contacts_file_edit.setMinimumHeight(36)
        self.contacts_file_edit.setStyleSheet(
            "padding: 0 8px; background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px;"
        )
        contacts_layout.addWidget(self.contacts_file_edit, 1)  # 1 = stretch factor
        
        browse_button = QPushButton(qta.icon('fa5s.folder-open'), "")
        browse_button.setToolTip("Browse for contacts file")
        browse_button.setFixedSize(36, 36)
        browse_button.setStyleSheet(
            "QPushButton { background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px; }"
            "QPushButton:hover { background-color: #e9ecef; }"
        )
        browse_button.clicked.connect(self.browse_contacts_file)
        contacts_layout.addWidget(browse_button)
        
        config_layout.addRow(contacts_label, contacts_layout)
        
        # Email limit
        limit_label = QLabel("Limit:")
        limit_label.setStyleSheet("font-weight: 500;")
        
        self.limit_spin = QSpinBox()
        self.limit_spin.setRange(0, 1000)
        self.limit_spin.setValue(0)
        self.limit_spin.setSpecialValueText("No limit")
        self.limit_spin.setMinimumHeight(36)
        self.limit_spin.setMinimumWidth(120)
        self.limit_spin.setStyleSheet(
            "padding: 0 8px; border: 1px solid #ced4da; border-radius: 4px;"
        )
        
        config_layout.addRow(limit_label, self.limit_spin)
        
        # Test mode checkbox
        self.test_mode_check = QCheckBox("Test Mode (don't actually send emails)")
        self.test_mode_check.setChecked(True)
        self.test_mode_check.setStyleSheet("font-weight: 500; margin-top: 8px;")
        
        config_layout.addRow("", self.test_mode_check)
        
        # Test connection button
        self.test_button = QPushButton(qta.icon('fa5s.vial'), "Test Connection")
        self.test_button.setObjectName("test_button")
        self.test_button.setMinimumHeight(40)
        self.test_button.setStyleSheet(
            "QPushButton#test_button { background-color: #20c997; color: white; font-weight: 500; }"
            "QPushButton#test_button:hover { background-color: #1db386; }"
            "QPushButton#test_button:pressed { background-color: #199473; }"
        )
        self.test_button.clicked.connect(self.test_email_connection)
        
        config_layout.addRow("", self.test_button)
        
        # ===== RIGHT SIDE - PREVIEW =====
        preview_group = QGroupBox("Email Preview")
        preview_group.setStyleSheet("QGroupBox { font-weight: 600; }")
        preview_layout = QVBoxLayout(preview_group)
        preview_layout.setSpacing(12)
        preview_layout.setContentsMargins(16, 24, 16, 16)
        
        # Subject preview
        subject_layout = QFormLayout()
        subject_layout.setSpacing(8)
        subject_layout.setContentsMargins(0, 0, 0, 0)
        
        subject_label = QLabel("Subject:")
        subject_label.setStyleSheet("font-weight: 500;")
        
        self.subject_label = QLabel("N/A")
        self.subject_label.setStyleSheet(
            "color: #495057; background-color: #f8f9fa; padding: 10px; "
            "border: 1px solid #ced4da; border-radius: 4px;"
        )
        self.subject_label.setWordWrap(True)
        self.subject_label.setMinimumHeight(36)
        
        subject_layout.addRow(subject_label, self.subject_label)
        preview_layout.addLayout(subject_layout)
        
        # Body preview
        body_label = QLabel("Body:")
        body_label.setStyleSheet("font-weight: 500;")
        preview_layout.addWidget(body_label)
        
        self.body_preview = QTextEdit()
        self.body_preview.setReadOnly(True)
        self.body_preview.setMinimumHeight(200)
        self.body_preview.setStyleSheet(
            "background-color: #f8f9fa; padding: 12px; "
            "border: 1px solid #ced4da; border-radius: 4px;"
        )
        preview_layout.addWidget(self.body_preview)
        
        # Update preview when template changes
        self.template_combo.currentTextChanged.connect(self.update_preview)
        self.template_combo.currentIndexChanged.connect(self.update_preview)
        
        # Add configuration and preview to top layout
        top_layout.addWidget(config_group, 40)  # 40% width
        top_layout.addWidget(preview_group, 60)  # 60% width
        
        # Bottom section - Progress
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        bottom_layout.setContentsMargins(0, 0, 0, 0)
        
        progress_group = QGroupBox("Progress")
        progress_group.setStyleSheet("QGroupBox { font-weight: 600; }")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(12)
        progress_layout.setContentsMargins(16, 24, 16, 16)
        
        # Progress bar with improved styling
        progress_label = QLabel("Status:")
        progress_label.setStyleSheet("font-weight: 500;")
        progress_layout.addWidget(progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setMinimumHeight(16)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setStyleSheet(
            "QProgressBar { background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px; text-align: center; }"
            "QProgressBar::chunk { background-color: #4361ee; border-radius: 3px; }"
        )
        progress_layout.addWidget(self.progress_bar)
        
        # Log with improved styling
        log_label = QLabel("Log:")
        log_label.setStyleSheet("font-weight: 500;")
        progress_layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(150)
        self.log_text.setStyleSheet(
            "font-family: 'Consolas', monospace; background-color: #f8f9fa; padding: 12px; "
            "border: 1px solid #ced4da; border-radius: 4px;"
        )
        progress_layout.addWidget(self.log_text)
        
        bottom_layout.addWidget(progress_group)
        
        # Add widgets to splitter
        main_splitter.addWidget(top_widget)
        main_splitter.addWidget(bottom_widget)
        main_splitter.setSizes([600, 400])  # Initial sizes
        
        layout.addWidget(main_splitter)
        
        # Action button with improved styling
        self.send_button = QPushButton(qta.icon('fa5s.paper-plane'), "Send Emails")
        self.send_button.setMinimumWidth(220)
        self.send_button.setMinimumHeight(48)
        self.send_button.setStyleSheet(
            "QPushButton { background-color: #4361ee; color: white; font-weight: 600; font-size: 14px; }"
            "QPushButton:hover { background-color: #3a56d4; }"
            "QPushButton:pressed { background-color: #2c4bc7; }"
            "QPushButton:disabled { background-color: #ced4da; color: #6c757d; }"
        )
        self.send_button.clicked.connect(self.send_emails)
        
        # Center the send button
        send_button_layout = QHBoxLayout()
        send_button_layout.addStretch()
        send_button_layout.addWidget(self.send_button)
        send_button_layout.addStretch()
        
        layout.addLayout(send_button_layout)
        
        # Update the preview initially
        self.update_preview()
    
    def refresh_templates(self):
        """Refresh the templates list"""
        self.template_combo.clear()
        templates = self.template_manager.get_templates()

        if not templates:
            # Add a placeholder if no templates found
            self.template_combo.addItem("No templates found", None)
            return

        for template in templates:
            # Use template ID as the data and name as display text
            template_name = template.get('name', template.get('id', 'Unknown'))
            template_id = template.get('id', template.get('name', ''))
            self.template_combo.addItem(template_name, template_id)

        # Select first template by default
        if self.template_combo.count() > 0:
            self.template_combo.setCurrentIndex(0)
    
    def update_preview(self):
        """Update the email preview based on the selected template"""
        # Get template ID from combo box data
        template_id = self.template_combo.currentData()
        if not template_id:
            self.subject_label.setText("No template selected")
            self.body_preview.setText("Please select a template to preview the email content.")
            return

        template = self.template_manager.get_template(template_id)
        if not template:
            self.subject_label.setText("Template not found")
            self.body_preview.setText("Selected template could not be loaded.")
            return

        # Update subject
        subject = template.get('subject', 'No subject')
        self.subject_label.setText(subject)

        # Update body with proper formatting
        body = template.get('body', 'No body content')
        # Replace \n with actual line breaks for display
        formatted_body = body.replace('\\n', '\n')
        self.body_preview.setText(formatted_body)

        # Set cursor to beginning
        cursor = self.body_preview.textCursor()
        cursor.movePosition(cursor.MoveOperation.Start)
        self.body_preview.setTextCursor(cursor)
    
    def browse_contacts_file(self):
        """Open file dialog to select contacts file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Contacts File",
            "",
            "Excel Files (*.xlsx *.xls);;CSV Files (*.csv);;All Files (*.*)"
        )
        if file_path:
            self.contacts_file_edit.setText(file_path)
    
    def send_emails(self):
        """Send emails based on the current configuration"""
        template_id = self.template_combo.currentData()
        contacts_file = self.contacts_file_edit.text()
        test_mode = self.test_mode_check.isChecked()
        limit = self.limit_spin.value() if self.limit_spin.value() > 0 else None

        # Validate inputs
        if not template_id:
            QMessageBox.warning(self, "Missing Template", "Please select an email template.")
            return
        
        if not contacts_file:
            QMessageBox.warning(self, "Missing Contacts", "Please select a contacts file.")
            return
        
        # Confirm sending
        if not test_mode:
            confirm = QMessageBox.question(
                self,
                "Confirm Send",
                f"You are about to send real emails to contacts in {contacts_file}.\n\n"
                f"Are you sure you want to continue?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if confirm != QMessageBox.StandardButton.Yes:
                return
        
        # Clear log and reset progress
        self.log_text.clear()
        self.progress_bar.setValue(0)
        
        # Disable send button during operation
        self.send_button.setEnabled(False)
        
        # Create and start worker thread
        self.worker = EmailSenderWorker(
            self.email_sender,
            self.template_manager,
            template_id,
            contacts_file,
            test_mode,
            limit
        )
        
        # Connect signals
        self.worker.progress_update.connect(self.update_progress)
        self.worker.finished.connect(self.sending_finished)
        self.worker.error.connect(self.handle_error)
        
        # Start the worker
        self.worker.start()
        
        # Log start
        mode = "TEST MODE" if test_mode else "LIVE MODE"
        template_name = self.template_combo.currentText()
        self.log(f"Starting email sending in {mode}")
        self.log(f"Template: {template_name}")
        self.log(f"Contacts: {contacts_file}")
        if limit:
            self.log(f"Limit: {limit} contacts")
        self.log("-----------------------------------")
    
    def update_progress(self, index, message):
        """Update progress bar and log"""
        # Update progress bar based on index
        # Note: We don't have total count here, so we'll update it in the worker
        _ = index  # Suppress unused parameter warning
        self.log(message)
    
    def sending_finished(self, successful, total):
        """Handle completion of email sending"""
        self.progress_bar.setValue(100)
        self.log("-----------------------------------")
        self.log(f"Email sending complete: {successful} of {total} emails sent successfully")
        self.send_button.setEnabled(True)
        
        QMessageBox.information(
            self,
            "Email Sending Complete",
            f"Successfully sent {successful} of {total} emails."
        )
    
    def handle_error(self, error_message):
        """Handle errors during email sending"""
        self.log(f"ERROR: {error_message}")
        self.send_button.setEnabled(True)
        
        QMessageBox.critical(
            self,
            "Error Sending Emails",
            error_message
        )
    
    def log(self, message):
        """Add a message to the log"""
        self.log_text.append(message)
        # Scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        
    def test_email_connection(self):
        """Test the email connection without sending an actual email"""
        # Disable the test button during testing
        self.test_button.setEnabled(False)
        self.test_button.setText("Testing...")
        
        # Clear log
        self.log_text.clear()
        self.log("Testing email connection...")
        
        try:
            # Get the email settings from the email sender
            settings = self.email_sender.get_settings()
            
            if not settings.get('smtp_server') or not settings.get('smtp_port'):
                self.log("ERROR: SMTP server settings not configured")
                self.log("Please go to Settings tab and configure your email settings")
                QMessageBox.warning(
                    self,
                    "Settings Not Configured",
                    "Email settings are not properly configured.\n\n"
                    "Please go to the Settings tab and configure your SMTP server settings."
                )
                return
                
            # Test the connection
            self.log(f"Connecting to {settings.get('smtp_server')}:{settings.get('smtp_port')}...")
            
            # Attempt to connect to the SMTP server
            result = self.email_sender.test_connection()
            
            if result['success']:
                self.log("✓ Connection successful!")
                self.log(f"Connected to {settings.get('smtp_server')}:{settings.get('smtp_port')}")
                self.log(f"Authentication method: {settings.get('auth_method', 'Standard')}")
                self.log(f"From email: {settings.get('from_email')}")
                
                QMessageBox.information(
                    self,
                    "Connection Successful",
                    f"Successfully connected to {settings.get('smtp_server')}\n\n"
                    f"Your email settings are correctly configured."
                )
            else:
                self.log(f"✗ Connection failed: {result.get('error', 'Unknown error')}")
                
                QMessageBox.critical(
                    self,
                    "Connection Failed",
                    f"Failed to connect to {settings.get('smtp_server')}\n\n"
                    f"Error: {result.get('error', 'Unknown error')}\n\n"
                    "Please check your email settings in the Settings tab."
                )
        except Exception as e:
            self.log(f"✗ Error testing connection: {str(e)}")
            
            QMessageBox.critical(
                self,
                "Connection Error",
                f"An error occurred while testing the connection:\n\n{str(e)}"
            )
        finally:
            # Re-enable the test button
            self.test_button.setEnabled(True)
            self.test_button.setText("Test Connection")
