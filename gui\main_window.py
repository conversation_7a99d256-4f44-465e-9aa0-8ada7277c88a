"""
Main window for the Cold Emailer GUI application
"""
import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QVBoxLayout, QWidget,
    QMessageBox, QLabel, QStatusBar
)
from PyQt6.QtGui import QAction, QFont
from PyQt6.QtCore import Qt
import qtawesome as qta
from utils import create_default_dirs

# Import tab components
from gui.components.email_sender_tab import EmailSenderTab
from gui.components.contacts_tab import ContactsTab
from gui.components.templates_tab import TemplatesTab
from gui.components.settings_tab import SettingsTab

class MainWindow(QMainWindow):
    """Main window for the Cold Emailer GUI application"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Cold Emailer Pro")
        self.resize(1000, 700)  # Set a reasonable default size
        
        # Create default directories
        create_default_dirs()
        
        self.init_ui()
        
        # Set application icon
        self.setWindowIcon(qta.icon('fa5s.envelope'))
        
        # Create the central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create header
        header_layout = QVBoxLayout()
        header_label = QLabel("Cold Emailer Pro")
        header_label.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(header_label)
        
        # Add a subtitle
        subtitle_label = QLabel("Send personalized emails efficiently")
        subtitle_label.setFont(QFont("Segoe UI", 10))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #666;")
        header_layout.addWidget(subtitle_label)
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tabs = QTabWidget()
        self.tabs.setFont(QFont("Segoe UI", 10))
        self.tabs.setTabPosition(QTabWidget.TabPosition.North)
        self.tabs.setDocumentMode(True)
        
        # Create tabs
        self.email_sender_tab = EmailSenderTab()
        self.contacts_tab = ContactsTab()
        self.templates_tab = TemplatesTab()
        self.settings_tab = SettingsTab()
        
        # Add tabs to tab widget with icons
        self.tabs.addTab(self.email_sender_tab, qta.icon('fa5s.paper-plane'), "Send Emails")
        self.tabs.addTab(self.contacts_tab, qta.icon('fa5s.address-book'), "Contacts")
        self.tabs.addTab(self.templates_tab, qta.icon('fa5s.file-alt'), "Templates")
        self.tabs.addTab(self.settings_tab, qta.icon('fa5s.cog'), "Settings")
        
        main_layout.addWidget(self.tabs)
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
        
        # Set up menu bar
        self.setup_menu()
        
        # Apply stylesheet
        self.apply_stylesheet()
    
    def init_ui(self):
        self.setMinimumSize(1000, 700)
    
    def setup_menu(self):
        """Set up the application menu bar"""
        menu_bar = self.menuBar()
        
        # File menu
        file_menu = menu_bar.addMenu("&File")
        
        # Exit action
        exit_action = QAction(qta.icon('fa5s.sign-out-alt'), "&Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menu_bar.addMenu("&Help")
        
        # About action
        about_action = QAction(qta.icon('fa5s.info-circle'), "&About", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
        
        # Documentation action
        docs_action = QAction(qta.icon('fa5s.book'), "&Documentation", self)
        docs_action.triggered.connect(self.show_documentation)
        help_menu.addAction(docs_action)
    
    def show_about_dialog(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Cold Emailer Pro",
            "Cold Emailer Pro v1.0.0\n\n"
            "A modern GUI application for sending personalized cold emails.\n\n"
            "2023-2025 Cold Emailer Pro"
        )
    
    def show_documentation(self):
        """Show documentation dialog"""
        QMessageBox.information(
            self,
            "Documentation",
            "Cold Emailer Pro Documentation\n\n"
            "For detailed documentation, please visit:\n"
            "https://github.com/AndrxwWxng/cold-emailer\n\n"
            "Quick Start:\n"
            "1. Configure your email settings in the Settings tab\n"
            "2. Create or import contacts in the Contacts tab\n"
            "3. Create email templates in the Templates tab\n"
            "4. Send emails using the Send Emails tab"
        )
    
    def apply_stylesheet(self):
        """Apply a modern stylesheet to the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: none;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: transparent;
                border: none;
                padding: 10px 20px;
                margin-right: 4px;
                color: #6c757d;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background-color: #4361ee;
                border-radius: 6px;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e9ecef;
                border-radius: 6px;
            }
            QPushButton {
                background-color: #4361ee;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #3a56d4;
            }
            QPushButton:pressed {
                background-color: #2c4bc7;
            }
            QPushButton:disabled {
                background-color: #ced4da;
                color: #6c757d;
            }
            QPushButton#test_button {
                background-color: #20c997;
            }
            QPushButton#test_button:hover {
                background-color: #1db386;
            }
            QPushButton#test_button:pressed {
                background-color: #199473;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                background-color: white;
                min-height: 24px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
                border: 1px solid #4361ee;
            }
            QLabel {
                color: #212529;
                font-weight: 400;
            }
            QGroupBox {
                border: 1px solid #e9ecef;
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 16px;
                font-weight: 500;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #4361ee;
            }
            QProgressBar {
                border: none;
                border-radius: 4px;
                background-color: #e9ecef;
                text-align: center;
                min-height: 12px;
            }
            QProgressBar::chunk {
                background-color: #4361ee;
                border-radius: 4px;
            }
            QCheckBox {
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 1px solid #ced4da;
            }
            QCheckBox::indicator:checked {
                background-color: #4361ee;
                border: 1px solid #4361ee;
            }
            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #ced4da;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

def main():
    """Main entry point for the GUI application"""
    app = QApplication(sys.argv)
    app.setStyle("Fusion")  # Use Fusion style for a modern look
    
    # Set the application name and organization
    app.setApplicationName("Cold Emailer Pro")
    app.setOrganizationName("Cold Emailer")
    
    # Create and show the main window
    window = MainWindow()
    window.show()
    
    # Start the event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
