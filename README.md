# Cold Emailer Pro 🚀

A powerful, flexible, and easy-to-use cold emailing tool built with Python. Send personalized cold emails at scale with templates, contact management, and email tracking. Now with a modern, sleek GUI interface!

## 🔧 Recent Fixes & Improvements

### ✅ Fixed Issues

- **Import Structure**: Fixed all broken import statements throughout the codebase
- **File Organization**: Properly organized files and cleaned up the project structure
- **UI Functionality**: Fixed all major UI functionality issues and errors
- **Template System**: Improved template management with proper variable substitution
- **Contact Management**: Enhanced contact import/export functionality
- **Error Handling**: Added comprehensive error handling and validation
- **Path Issues**: Fixed all file path and configuration loading issues

### 🎨 UI Improvements

- **Modern Design**: Clean, professional interface with improved styling
- **Better Layout**: Optimized layouts with proper spacing and proportions
- **Visual Feedback**: Enhanced progress tracking and status indicators
- **Responsive Elements**: Improved button states and user interactions
- **Clean Code**: Removed unused imports and cleaned up code structure

## ✨ Features

- **Modern GUI Interface**: Clean, intuitive graphical user interface for easy operation
- **Template Management**: Create, edit, and manage email templates with variables
- **Contact Management**: Import, organize, and segment your contacts
- **Personalization**: Use variables to personalize each email
- **Rate Limiting**: Avoid being flagged as spam with configurable sending limits
- **Logging**: Detailed logs for tracking email delivery and errors
- **CLI Interface**: Simple command-line interface for all operations (still available)
- **Multiple Formats**: Supports CSV, JSON, and Excel for contact management
- **Live Preview**: See how your emails will look before sending
- **Test Mode**: Test your email campaigns without actually sending emails

## 🚀 Getting Started

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/cold-emailer-pro.git
   cd cold-emailer-pro
   ```

2. Install the required packages:

   ```bash
   pip install -r requirements.txt
   ```

3. Set up your environment variables:

   - Copy `.env.example` to `.env`
   - Update the values in `.env` with your email credentials

   ```ini
   EMAIL_ADDRESS=<EMAIL>
   EMAIL_PASSWORD=your-email-password
   ```

   > **Note**: For Gmail, you may need to generate an "App Password" if you have 2FA enabled.

## 🛠 Usage

### GUI Application

To start the GUI application, simply run:

```bash
python main.py
```

Or double-click the `start_app.bat` file on Windows.

The GUI application provides intuitive interfaces for:

- Sending emails with a visual preview
- Managing contacts with import/export capabilities
- Creating and editing email templates
- Configuring email settings

### Command Line Interface (CLI)

#### Send Emails

```bash
python cli.py send professional --contacts contacts/sample_contacts.csv --test
```

#### List Contacts

```bash
python cli.py contacts list
```

#### Add a Contact

```bash
python cli.py <NAME_EMAIL> --first-name John --last-name Doe --company "Acme Inc" --position "CTO"
```

#### List Templates

```bash
python cli.py templates list
```

#### Create a New Template

```bash
python cli.py templates create --name my_template --subject "Hello {first_name}" --body "Dear {first_name},..."
```

### Configuration

Edit `config.json` to customize the emailer's behavior:

```json
{
  "email": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "default_sender_name": "Your Name",
    "default_subject": "Cold Email - {subject}",
    "default_interval_seconds": 5,
    "max_emails_per_day": 100
  },
  "templates": {
    "default_template": "professional",
    "templates_dir": "templates"
  },
  "contacts": {
    "default_file": "contacts/contacts.csv",
    "backup_dir": "backups"
  }
}
```

## 📁 Project Structure

```
cold-emailer-pro/
├── cli.py                # Command-line interface
├── config.json           # Configuration file
├── contacts/             # Directory for contact lists
│   └── sample_contacts.csv  # Example contacts
├── email_sender.py       # Email sending functionality
├── contacts_manager.py   # Contact management
├── template_manager.py   # Template management
├── requirements.txt      # Python dependencies
├── .env.example         # Example environment variables
├── .env                 # Your environment variables (create this)
└── templates/           # Email templates
    ├── professional.json
    └── research.json
```

## 📧 Email Templates

Templates are stored as JSON files in the `templates` directory. Each template should have:

- `name`: Template identifier
- `description`: Brief description of the template's purpose
- `subject`: Email subject line (can include variables like `{name}`)
- `body`: Email body (can include variables like `{name}`)
- `tags`: Optional tags for organization

Example template (`templates/professional.json`):

```json
{
  "name": "professional",
  "description": "A professional cold email template",
  "tags": ["business", "outreach"],
  "subject": "Quick question about {company_name}",
  "body": "Hello {first_name},\n\nI hope this email finds you well..."
}
```

## 📊 Contact Management

Contacts can be managed in CSV, JSON, or Excel format. The default format is CSV with these columns:

- `email` (required)
- `first_name` (required)
- `last_name` (required)
- `company`
- `position`
- `phone`
- `tags` (comma-separated)
- `notes`
- `last_contacted`
- `status`

## 🔒 Security

- Never commit your `.env` file to version control
- Use an app password instead of your main email password
- Be mindful of email sending limits to avoid being flagged as spam

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📬 Contact

Andrew Wang - <EMAIL>

Project Link: [https://github.com/AndrxwWxng/Cold-Emailer](https://github.com/AndrxwWxng/Cold-Emailer)
