"""
Main entry point for Cold Emailer GUI
"""
import sys
from PyQt6.QtWidgets import QApplication
from gui.main_window import MainWindow

def main():
    """Main entry point for the GUI application"""
    app = QApplication(sys.argv)
    app.setStyle("Fusion")  # Use Fusion style for a modern look

    # Set the application name and organization
    app.setApplicationName("Cold Emailer Pro")
    app.setOrganizationName("Cold Emailer")

    # Create and show the main window
    window = MainWindow()
    window.show()

    # Start the event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
