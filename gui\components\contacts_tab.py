"""
Contacts Tab for Cold Emailer GUI
"""
import os
import sys
import pandas as pd
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QLineEdit, QFileDialog, QTableWidget, QTableWidgetItem, QHeaderView,
    QGroupBox, QFormLayout, QMessageBox, QDialog, QDialogButtonBox,
    QCheckBox, QTextEdit
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QFont
import qtawesome as qta

# Import the contacts manager module
from contacts_manager import ContactsManager

class AddContactDialog(QDialog):
    """Dialog for adding a new contact"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Add New Contact")
        self.setMinimumWidth(400)
        self.init_ui()
    
    def init_ui(self):
        """Initialize the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Create form layout
        form_layout = QFormLayout()
        
        # First name field
        self.first_name_edit = QLineEdit()
        form_layout.addRow("First Name:", self.first_name_edit)
        
        # Last name field
        self.last_name_edit = QLineEdit()
        form_layout.addRow("Last Name:", self.last_name_edit)
        
        # Email field
        self.email_edit = QLineEdit()
        form_layout.addRow("Email:", self.email_edit)
        
        # Institution field
        self.institution_edit = QLineEdit()
        form_layout.addRow("Institution:", self.institution_edit)
        
        # Department field
        self.department_edit = QLineEdit()
        form_layout.addRow("Department:", self.department_edit)
        
        # Position field
        self.position_edit = QLineEdit()
        form_layout.addRow("Position:", self.position_edit)
        
        # Research area field
        self.research_area_edit = QLineEdit()
        form_layout.addRow("Research Area:", self.research_area_edit)
        
        # Notes field
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow("Notes:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # Add buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_contact_data(self):
        """Get the contact data from the form"""
        return {
            'first_name': self.first_name_edit.text(),
            'last_name': self.last_name_edit.text(),
            'email': self.email_edit.text(),
            'institution': self.institution_edit.text(),
            'department': self.department_edit.text(),
            'position': self.position_edit.text(),
            'research_area': self.research_area_edit.text(),
            'notes': self.notes_edit.toPlainText()
        }

class ContactsTab(QWidget):
    """Tab for managing contacts"""
    
    def __init__(self):
        super().__init__()
        self.contacts_manager = ContactsManager()
        self.current_file = None
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # File selection section
        file_group = QGroupBox("Contacts File")
        file_layout = QHBoxLayout()
        
        self.file_edit = QLineEdit()
        self.file_edit.setPlaceholderText("Select or create a contacts file")
        self.file_edit.setReadOnly(True)
        
        browse_button = QPushButton(qta.icon('fa5s.folder-open'), "Browse")
        browse_button.clicked.connect(self.browse_contacts_file)
        
        new_file_button = QPushButton(qta.icon('fa5s.file'), "New File")
        new_file_button.clicked.connect(self.create_new_file)
        
        file_layout.addWidget(self.file_edit)
        file_layout.addWidget(browse_button)
        file_layout.addWidget(new_file_button)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # Contacts table
        table_group = QGroupBox("Contacts")
        table_layout = QVBoxLayout()
        
        self.contacts_table = QTableWidget()
        self.contacts_table.setColumnCount(6)
        self.contacts_table.setHorizontalHeaderLabels(
            ["Name", "Email", "Institution", "Position", "Research Area", "Notes"]
        )
        self.contacts_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.contacts_table.setAlternatingRowColors(True)
        self.contacts_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        table_layout.addWidget(self.contacts_table)
        
        # Table actions
        actions_layout = QHBoxLayout()
        
        self.add_button = QPushButton(qta.icon('fa5s.user-plus'), "Add Contact")
        self.add_button.clicked.connect(self.add_contact)
        actions_layout.addWidget(self.add_button)
        
        self.import_button = QPushButton(qta.icon('fa5s.file-import'), "Import")
        self.import_button.clicked.connect(self.import_contacts)
        actions_layout.addWidget(self.import_button)
        
        self.export_button = QPushButton(qta.icon('fa5s.file-export'), "Export")
        self.export_button.clicked.connect(self.export_contacts)
        actions_layout.addWidget(self.export_button)
        
        actions_layout.addStretch()
        
        self.delete_button = QPushButton(qta.icon('fa5s.user-minus'), "Delete Selected")
        self.delete_button.clicked.connect(self.delete_selected_contacts)
        actions_layout.addWidget(self.delete_button)
        
        table_layout.addLayout(actions_layout)
        
        table_group.setLayout(table_layout)
        layout.addWidget(table_group)
        
        # Statistics
        stats_layout = QHBoxLayout()
        self.stats_label = QLabel("No contacts loaded")
        stats_layout.addWidget(self.stats_label)
        
        layout.addLayout(stats_layout)
        
        # Disable buttons initially
        self.update_button_states()
    
    def update_button_states(self):
        """Update button states based on current state"""
        has_file = self.current_file is not None
        has_selection = len(self.contacts_table.selectedItems()) > 0
        
        self.add_button.setEnabled(has_file)
        self.import_button.setEnabled(has_file)
        self.export_button.setEnabled(has_file and self.contacts_table.rowCount() > 0)
        self.delete_button.setEnabled(has_file and has_selection)
    
    def browse_contacts_file(self):
        """Open file dialog to select contacts file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Contacts File",
            "",
            "Excel Files (*.xlsx *.xls);;CSV Files (*.csv);;All Files (*.*)"
        )
        if file_path:
            self.load_contacts_file(file_path)
    
    def create_new_file(self):
        """Create a new contacts file"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Create New Contacts File",
            "",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        if file_path:
            # Create empty DataFrame and save
            df = pd.DataFrame(columns=[
                'first_name', 'last_name', 'email', 'institution',
                'department', 'position', 'research_area', 'notes'
            ])
            
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False)
            elif file_path.endswith('.csv'):
                df.to_csv(file_path, index=False)
            
            self.load_contacts_file(file_path)
    
    def load_contacts_file(self, file_path):
        """Load contacts from file"""
        try:
            # Load the file
            if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                QMessageBox.warning(self, "Unsupported Format", "Unsupported file format.")
                return
            
            # Update UI
            self.current_file = file_path
            self.file_edit.setText(file_path)
            
            # Populate table
            self.populate_contacts_table(df)
            
            # Update button states
            self.update_button_states()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading contacts file: {str(e)}")
    
    def populate_contacts_table(self, df):
        """Populate the contacts table with data from DataFrame"""
        # Clear existing data
        self.contacts_table.setRowCount(0)
        
        # Map DataFrame columns to our expected columns
        column_mapping = {
            'first_name': 'first_name',
            'last_name': 'last_name',
            'Professor Name': 'Professor Name',
            'email': 'email',
            'Professor Email': 'Professor Email',
            'institution': 'institution',
            'position': 'position',
            'research_area': 'research_area',
            'Research Area': 'Research Area',
            'notes': 'notes'
        }
        
        # Add rows
        for i, row in df.iterrows():
            self.contacts_table.insertRow(i)
            
            # Name (combine first_name and last_name if available)
            if 'first_name' in df.columns and 'last_name' in df.columns:
                name = f"{row.get('first_name', '')} {row.get('last_name', '')}".strip()
            elif 'Professor Name' in df.columns:
                name = row.get('Professor Name', '')
            else:
                name = ""
            
            # Email
            if 'email' in df.columns:
                email = row.get('email', '')
            elif 'Professor Email' in df.columns:
                email = row.get('Professor Email', '')
            else:
                email = ""
            
            # Institution
            institution = row.get('institution', '')
            
            # Position
            position = row.get('position', '')
            
            # Research Area
            if 'research_area' in df.columns:
                research_area = row.get('research_area', '')
            elif 'Research Area' in df.columns:
                research_area = row.get('Research Area', '')
            else:
                research_area = ""
            
            # Notes
            notes = row.get('notes', '')
            
            # Set table items
            self.contacts_table.setItem(i, 0, QTableWidgetItem(name))
            self.contacts_table.setItem(i, 1, QTableWidgetItem(email))
            self.contacts_table.setItem(i, 2, QTableWidgetItem(institution))
            self.contacts_table.setItem(i, 3, QTableWidgetItem(position))
            self.contacts_table.setItem(i, 4, QTableWidgetItem(research_area))
            self.contacts_table.setItem(i, 5, QTableWidgetItem(notes))
        
        # Update statistics
        self.update_statistics()
    
    def update_statistics(self):
        """Update the statistics label"""
        count = self.contacts_table.rowCount()
        if count == 0:
            self.stats_label.setText("No contacts loaded")
        else:
            self.stats_label.setText(f"{count} contacts loaded")
    
    def add_contact(self):
        """Add a new contact"""
        if not self.current_file:
            QMessageBox.warning(self, "No File Selected", "Please select or create a contacts file first.")
            return
        
        dialog = AddContactDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            contact_data = dialog.get_contact_data()
            
            # Validate email
            if not contact_data['email']:
                QMessageBox.warning(self, "Missing Email", "Email address is required.")
                return
            
            # Add to table
            row = self.contacts_table.rowCount()
            self.contacts_table.insertRow(row)
            
            name = f"{contact_data['first_name']} {contact_data['last_name']}".strip()
            
            self.contacts_table.setItem(row, 0, QTableWidgetItem(name))
            self.contacts_table.setItem(row, 1, QTableWidgetItem(contact_data['email']))
            self.contacts_table.setItem(row, 2, QTableWidgetItem(contact_data['institution']))
            self.contacts_table.setItem(row, 3, QTableWidgetItem(contact_data['position']))
            self.contacts_table.setItem(row, 4, QTableWidgetItem(contact_data['research_area']))
            self.contacts_table.setItem(row, 5, QTableWidgetItem(contact_data['notes']))
            
            # Save changes
            self.save_contacts()
            
            # Update statistics
            self.update_statistics()
    
    def delete_selected_contacts(self):
        """Delete selected contacts"""
        selected_rows = set()
        for item in self.contacts_table.selectedItems():
            selected_rows.add(item.row())
        
        if not selected_rows:
            return
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete {len(selected_rows)} contact(s)?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm != QMessageBox.StandardButton.Yes:
            return
        
        # Delete rows in reverse order to avoid index shifting
        for row in sorted(selected_rows, reverse=True):
            self.contacts_table.removeRow(row)
        
        # Save changes
        self.save_contacts()
        
        # Update statistics
        self.update_statistics()
    
    def import_contacts(self):
        """Import contacts from another file"""
        if not self.current_file:
            QMessageBox.warning(self, "No File Selected", "Please select or create a contacts file first.")
            return
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Contacts From",
            "",
            "Excel Files (*.xlsx *.xls);;CSV Files (*.csv);;All Files (*.*)"
        )
        
        if not file_path:
            return
        
        try:
            # Load the file
            if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                import_df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                import_df = pd.read_csv(file_path)
            else:
                QMessageBox.warning(self, "Unsupported Format", "Unsupported file format.")
                return
            
            # Get current data
            current_df = self.get_current_data_as_df()
            
            # Merge DataFrames
            merged_df = pd.concat([current_df, import_df], ignore_index=True)
            
            # Populate table
            self.populate_contacts_table(merged_df)
            
            # Save changes
            self.save_contacts()
            
            QMessageBox.information(
                self,
                "Import Complete",
                f"Successfully imported {len(import_df)} contacts."
            )
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error importing contacts: {str(e)}")
    
    def export_contacts(self):
        """Export contacts to another file"""
        if not self.current_file or self.contacts_table.rowCount() == 0:
            QMessageBox.warning(self, "No Contacts", "No contacts to export.")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Contacts To",
            "",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        
        if not file_path:
            return
        
        try:
            # Get current data
            df = self.get_current_data_as_df()
            
            # Save to file
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False)
            elif file_path.endswith('.csv'):
                df.to_csv(file_path, index=False)
            
            QMessageBox.information(
                self,
                "Export Complete",
                f"Successfully exported {len(df)} contacts to {file_path}."
            )
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error exporting contacts: {str(e)}")
    
    def get_current_data_as_df(self):
        """Get current table data as DataFrame"""
        data = []
        
        for row in range(self.contacts_table.rowCount()):
            name = self.contacts_table.item(row, 0).text()
            email = self.contacts_table.item(row, 1).text()
            institution = self.contacts_table.item(row, 2).text()
            position = self.contacts_table.item(row, 3).text()
            research_area = self.contacts_table.item(row, 4).text()
            notes = self.contacts_table.item(row, 5).text()
            
            # Split name into first and last name
            name_parts = name.split(maxsplit=1)
            first_name = name_parts[0] if name_parts else ""
            last_name = name_parts[1] if len(name_parts) > 1 else ""
            
            data.append({
                'first_name': first_name,
                'last_name': last_name,
                'email': email,
                'institution': institution,
                'position': position,
                'research_area': research_area,
                'notes': notes,
                'Professor Name': name,
                'Professor Email': email,
                'Research Area': research_area
            })
        
        return pd.DataFrame(data)
    
    def save_contacts(self):
        """Save contacts to current file"""
        if not self.current_file:
            return
        
        try:
            df = self.get_current_data_as_df()
            
            if self.current_file.endswith('.xlsx'):
                df.to_excel(self.current_file, index=False)
            elif self.current_file.endswith('.csv'):
                df.to_csv(self.current_file, index=False)
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving contacts: {str(e)}")
