"""
Templates Tab for Cold Emailer GUI
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QListWidget, QListWidgetItem, QSplitter,
    QGroupBox, QFormLayout, QMessageBox, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import Qt
import qtawesome as qta

# Import the template manager module
from template_manager import TemplateManager

class TemplateDialog(QDialog):
    """Dialog for creating or editing a template"""
    
    def __init__(self, parent=None, template=None):
        super().__init__(parent)
        self.template = template
        self.setWindowTitle("Create Template" if not template else "Edit Template")
        self.setMinimumSize(600, 500)
        self.init_ui()
    
    def init_ui(self):
        """Initialize the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Create form layout
        form_layout = QFormLayout()
        
        # Template name field
        self.name_edit = QLineEdit()
        if self.template:
            self.name_edit.setText(self.template.get('name', ''))
            self.name_edit.setReadOnly(True)  # Don't allow changing name
        form_layout.addRow("Template Name:", self.name_edit)
        
        # Description field
        self.description_edit = QLineEdit()
        if self.template:
            self.description_edit.setText(self.template.get('description', ''))
        form_layout.addRow("Description:", self.description_edit)
        
        # Subject field
        self.subject_edit = QLineEdit()
        if self.template:
            self.subject_edit.setText(self.template.get('subject', ''))
        form_layout.addRow("Subject:", self.subject_edit)
        
        layout.addLayout(form_layout)
        
        # Body field
        body_group = QGroupBox("Email Body")
        body_layout = QVBoxLayout()
        
        self.body_edit = QTextEdit()
        self.body_edit.setMinimumHeight(200)
        if self.template:
            self.body_edit.setText(self.template.get('body', ''))
        
        # Add help text
        help_label = QLabel(
            "Use variables like {{first_name}}, {{last_name}}, {{email}}, {{research_area}} in your template."
        )
        help_label.setStyleSheet("color: #666;")
        
        body_layout.addWidget(help_label)
        body_layout.addWidget(self.body_edit)
        
        body_group.setLayout(body_layout)
        layout.addWidget(body_group)
        
        # Add buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_template_data(self):
        """Get the template data from the form"""
        return {
            'name': self.name_edit.text(),
            'description': self.description_edit.text(),
            'subject': self.subject_edit.text(),
            'body': self.body_edit.toPlainText()
        }

class TemplatesTab(QWidget):
    """Tab for managing email templates"""
    
    def __init__(self):
        super().__init__()
        self.template_manager = TemplateManager()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # Create splitter for templates list and preview
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Templates list section
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        list_layout.setContentsMargins(0, 0, 16, 0)
        
        # Templates list group box
        templates_group = QGroupBox("Email Templates")
        templates_group.setStyleSheet("QGroupBox { font-weight: 600; }")
        templates_group_layout = QVBoxLayout(templates_group)
        templates_group_layout.setContentsMargins(16, 24, 16, 16)
        templates_group_layout.setSpacing(12)
        
        # Templates list with improved styling
        self.templates_list = QListWidget()
        self.templates_list.setAlternatingRowColors(True)
        self.templates_list.setStyleSheet(
            "QListWidget { background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px; padding: 4px; }"
            "QListWidget::item { padding: 8px; border-bottom: 1px solid #e9ecef; }"
            "QListWidget::item:selected { background-color: #4361ee; color: white; }"
            "QListWidget::item:alternate { background-color: #f1f3f5; }"
        )
        self.templates_list.currentItemChanged.connect(self.on_template_selected)
        templates_group_layout.addWidget(self.templates_list)
        
        # Template actions with improved styling
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(8)
        
        button_style = (
            "QPushButton { background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px; padding: 8px 12px; }"
            "QPushButton:hover { background-color: #e9ecef; }"
            "QPushButton:pressed { background-color: #dee2e6; }"
            "QPushButton:disabled { background-color: #f8f9fa; color: #adb5bd; }"
        )
        
        self.new_button = QPushButton(qta.icon('fa5s.plus'), "New Template")
        self.new_button.setStyleSheet(button_style)
        self.new_button.clicked.connect(self.create_template)
        actions_layout.addWidget(self.new_button)
        
        self.edit_button = QPushButton(qta.icon('fa5s.edit'), "Edit")
        self.edit_button.setStyleSheet(button_style)
        self.edit_button.clicked.connect(self.edit_template)
        self.edit_button.setEnabled(False)
        actions_layout.addWidget(self.edit_button)
        
        self.delete_button = QPushButton(qta.icon('fa5s.trash'), "Delete")
        self.delete_button.setStyleSheet(button_style)
        self.delete_button.clicked.connect(self.delete_template)
        self.delete_button.setEnabled(False)
        actions_layout.addWidget(self.delete_button)
        
        templates_group_layout.addLayout(actions_layout)
        list_layout.addWidget(templates_group)
        
        # Template preview section
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(16, 0, 0, 0)
        
        # Template details group
        details_group = QGroupBox("Template Details")
        details_group.setStyleSheet("QGroupBox { font-weight: 600; }")
        details_layout = QVBoxLayout(details_group)
        details_layout.setContentsMargins(16, 24, 16, 16)
        details_layout.setSpacing(12)
        
        # Template info with improved styling
        self.preview_form = QFormLayout()
        self.preview_form.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        self.preview_form.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        self.preview_form.setSpacing(10)
        
        label_style = "font-weight: 500;"
        value_style = "padding: 6px; background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px;"
        
        name_label = QLabel("Name:")
        name_label.setStyleSheet(label_style)
        self.name_label = QLabel("N/A")
        self.name_label.setStyleSheet(value_style)
        self.preview_form.addRow(name_label, self.name_label)
        
        description_label = QLabel("Description:")
        description_label.setStyleSheet(label_style)
        self.description_label = QLabel("N/A")
        self.description_label.setStyleSheet(value_style)
        self.preview_form.addRow(description_label, self.description_label)
        
        subject_label = QLabel("Subject:")
        subject_label.setStyleSheet(label_style)
        self.subject_label = QLabel("N/A")
        self.subject_label.setStyleSheet(value_style)
        self.preview_form.addRow(subject_label, self.subject_label)
        
        details_layout.addLayout(self.preview_form)
        preview_layout.addWidget(details_group)
        
        # Body preview with improved styling
        body_group = QGroupBox("Email Body")
        body_group.setStyleSheet("QGroupBox { font-weight: 600; }")
        body_layout = QVBoxLayout(body_group)
        body_layout.setContentsMargins(16, 24, 16, 16)
        body_layout.setSpacing(12)
        
        self.body_preview = QTextEdit()
        self.body_preview.setReadOnly(True)
        self.body_preview.setStyleSheet(
            "QTextEdit { background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px; padding: 12px; }"
        )
        body_layout.addWidget(self.body_preview)
        
        preview_layout.addWidget(body_group)
        
        # Variables section with improved styling
        variables_group = QGroupBox("Available Variables")
        variables_group.setStyleSheet("QGroupBox { font-weight: 600; }")
        variables_layout = QVBoxLayout(variables_group)
        variables_layout.setContentsMargins(16, 24, 16, 16)
        variables_layout.setSpacing(12)
        
        self.variables_label = QLabel("No variables detected")
        self.variables_label.setStyleSheet(
            "padding: 10px; background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px;"
        )
        variables_layout.addWidget(self.variables_label)
        
        preview_layout.addWidget(variables_group)
        
        # Add widgets to splitter
        splitter.addWidget(list_widget)
        splitter.addWidget(preview_widget)
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
        
        # Load templates
        self.load_templates()
    
    def load_templates(self):
        """Load templates from the template manager"""
        self.templates_list.clear()
        
        templates = self.template_manager.get_templates()
        for template in templates:
            item = QListWidgetItem(template['name'])
            item.setData(Qt.ItemDataRole.UserRole, template)
            self.templates_list.addItem(item)
    
    def on_template_selected(self, current, previous):
        """Handle template selection"""
        # previous parameter is required by Qt signal but not used
        if not current:
            self.clear_preview()
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            return
        
        # Enable buttons
        self.edit_button.setEnabled(True)
        self.delete_button.setEnabled(True)
        
        # Get template data
        template = current.data(Qt.ItemDataRole.UserRole)
        
        # Update preview
        self.name_label.setText(template.get('name', 'N/A'))
        self.description_label.setText(template.get('description', 'N/A'))
        self.subject_label.setText(template.get('subject', 'N/A'))
        self.body_preview.setText(template.get('body', ''))
        
        # Update variables
        variables = template.get('variables', [])
        if variables:
            self.variables_label.setText(', '.join([f"{{{{{v}}}}}" for v in variables]))
        else:
            self.variables_label.setText("No variables detected")
    
    def clear_preview(self):
        """Clear the template preview"""
        self.name_label.setText("N/A")
        self.description_label.setText("N/A")
        self.subject_label.setText("N/A")
        self.body_preview.setText("")
        self.variables_label.setText("No variables detected")
    
    def create_template(self):
        """Create a new template"""
        dialog = TemplateDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            template_data = dialog.get_template_data()
            
            # Validate inputs
            if not template_data['name']:
                QMessageBox.warning(self, "Missing Name", "Template name is required.")
                return
            
            if not template_data['subject']:
                QMessageBox.warning(self, "Missing Subject", "Email subject is required.")
                return
            
            if not template_data['body']:
                QMessageBox.warning(self, "Missing Body", "Email body is required.")
                return
            
            # Check for duplicate name
            if self.template_manager.get_template(template_data['name']):
                QMessageBox.warning(self, "Duplicate Name", "A template with this name already exists.")
                return
            
            # Create template
            success = self.template_manager.create_template(
                template_name=template_data['name'],
                subject=template_data['subject'],
                body=template_data['body'],
                description=template_data['description']
            )
            
            if success:
                # Reload templates
                self.load_templates()
                
                # Select the new template
                for i in range(self.templates_list.count()):
                    item = self.templates_list.item(i)
                    if item.text() == template_data['name']:
                        self.templates_list.setCurrentItem(item)
                        break
            else:
                QMessageBox.critical(self, "Error", "Failed to create template.")
    
    def edit_template(self):
        """Edit the selected template"""
        current_item = self.templates_list.currentItem()
        if not current_item:
            return
        
        template = current_item.data(Qt.ItemDataRole.UserRole)
        dialog = TemplateDialog(self, template)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            template_data = dialog.get_template_data()
            
            # Validate inputs
            if not template_data['subject']:
                QMessageBox.warning(self, "Missing Subject", "Email subject is required.")
                return
            
            if not template_data['body']:
                QMessageBox.warning(self, "Missing Body", "Email body is required.")
                return
            
            # Update template
            success = self.template_manager.update_template(
                template_name=template_data['name'],
                subject=template_data['subject'],
                body=template_data['body'],
                description=template_data['description']
            )
            
            if success:
                # Reload templates
                self.load_templates()
                
                # Re-select the template
                for i in range(self.templates_list.count()):
                    item = self.templates_list.item(i)
                    if item.text() == template_data['name']:
                        self.templates_list.setCurrentItem(item)
                        break
            else:
                QMessageBox.critical(self, "Error", "Failed to update template.")
    
    def delete_template(self):
        """Delete the selected template"""
        current_item = self.templates_list.currentItem()
        if not current_item:
            return
        
        template_name = current_item.text()
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete the template '{template_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm != QMessageBox.StandardButton.Yes:
            return
        
        # Delete template
        success = self.template_manager.delete_template(template_name)
        
        if success:
            # Reload templates
            self.load_templates()
            
            # Clear preview
            self.clear_preview()
            
            # Disable buttons
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
        else:
            QMessageBox.critical(self, "Error", "Failed to delete template.")
