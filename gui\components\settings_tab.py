"""
Settings Tab for Cold Emailer GUI
"""
import os
import json
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit,
    QGroupBox, QFormLayout, QSpinBox, QCheckBox, QMessageBox, QFileDialog
)
import qtawesome as qta

# Import the email sender module for testing connection
from email_sender import EmailSender

class SettingsTab(QWidget):
    """Tab for configuring application settings"""
    
    def __init__(self):
        super().__init__()
        self.config_path = 'config.json'
        self.config = self.load_config()
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Email settings section
        email_group = QGroupBox("Email Settings")
        email_layout = QFormLayout()
        
        # Email address
        self.email_edit = QLineEdit()
        self.email_edit.setText(self.config.get('email_address', ''))
        self.email_edit.setPlaceholderText("<EMAIL>")
        self.email_edit.setMinimumHeight(36)
        email_layout.addRow("Email Address:", self.email_edit)

        # Email password/app password
        self.password_edit = QLineEdit()
        self.password_edit.setText(self.config.get('email_password', ''))
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("App password recommended for Gmail")
        self.password_edit.setMinimumHeight(36)
        email_layout.addRow("Email Password:", self.password_edit)

        # SMTP server
        self.smtp_server_edit = QLineEdit()
        self.smtp_server_edit.setText(self.config.get('smtp_server', 'smtp.gmail.com'))
        self.smtp_server_edit.setMinimumHeight(36)
        email_layout.addRow("SMTP Server:", self.smtp_server_edit)

        # SMTP port
        self.smtp_port_spin = QSpinBox()
        self.smtp_port_spin.setRange(1, 65535)
        self.smtp_port_spin.setValue(self.config.get('smtp_port', 587))
        self.smtp_port_spin.setMinimumHeight(36)
        email_layout.addRow("SMTP Port:", self.smtp_port_spin)
        
        # Test connection button
        test_layout = QHBoxLayout()
        self.test_button = QPushButton(qta.icon('fa5s.vial'), "Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        test_layout.addWidget(self.test_button)
        test_layout.addStretch()
        email_layout.addRow("", test_layout)
        
        email_group.setLayout(email_layout)
        layout.addWidget(email_group)
        
        # Resume settings section
        resume_group = QGroupBox("Resume Settings")
        resume_layout = QFormLayout()
        
        # Resume file
        resume_file_layout = QHBoxLayout()
        resume_file_layout.setSpacing(8)

        self.resume_file_edit = QLineEdit()
        self.resume_file_edit.setText(self.config.get('resume_path', ''))
        self.resume_file_edit.setPlaceholderText("Path to your resume file")
        self.resume_file_edit.setReadOnly(True)
        self.resume_file_edit.setMinimumHeight(36)
        self.resume_file_edit.setStyleSheet(
            "padding: 0 8px; background-color: #f8f9fa; border: 1px solid #ced4da; border-radius: 4px;"
        )

        browse_button = QPushButton(qta.icon('fa5s.folder-open'), "Browse")
        browse_button.setMinimumHeight(36)
        browse_button.setStyleSheet(
            "QPushButton { background-color: #007bff; color: white; border: none; border-radius: 4px; padding: 8px 16px; }"
            "QPushButton:hover { background-color: #0056b3; }"
        )
        browse_button.clicked.connect(self.browse_resume_file)

        resume_file_layout.addWidget(self.resume_file_edit, 1)
        resume_file_layout.addWidget(browse_button)
        resume_layout.addRow("Resume File:", resume_file_layout)
        
        # Always attach resume checkbox
        self.attach_resume_check = QCheckBox("Always attach resume to emails")
        self.attach_resume_check.setChecked(self.config.get('always_attach_resume', True))
        resume_layout.addRow("", self.attach_resume_check)
        
        resume_group.setLayout(resume_layout)
        layout.addWidget(resume_group)
        
        # Email sending settings
        sending_group = QGroupBox("Sending Settings")
        sending_layout = QFormLayout()
        
        # Delay between emails
        self.delay_spin = QSpinBox()
        self.delay_spin.setRange(0, 60)
        self.delay_spin.setValue(self.config.get('delay_between_emails', 2))
        self.delay_spin.setSuffix(" seconds")
        self.delay_spin.setMinimumHeight(36)
        sending_layout.addRow("Delay Between Emails:", self.delay_spin)
        
        # Default to test mode
        self.default_test_mode_check = QCheckBox("Default to test mode")
        self.default_test_mode_check.setChecked(self.config.get('default_test_mode', True))
        sending_layout.addRow("", self.default_test_mode_check)
        
        sending_group.setLayout(sending_layout)
        layout.addWidget(sending_group)
        
        # Action buttons
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        self.save_button = QPushButton(qta.icon('fa5s.save'), "Save Settings")
        self.save_button.setMinimumHeight(40)
        self.save_button.setStyleSheet(
            "QPushButton { background-color: #28a745; color: white; border: none; border-radius: 4px; padding: 10px 20px; font-weight: 600; }"
            "QPushButton:hover { background-color: #218838; }"
        )
        self.save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_button)
        
        layout.addLayout(buttons_layout)
        
        # Add stretch to push everything to the top
        layout.addStretch()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    return json.load(f)
            else:
                return {}
        except Exception as e:
            print(f"Error loading config: {e}")
            return {}
    
    def save_config(self, config):
        """Save configuration to file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=4)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def browse_resume_file(self):
        """Open file dialog to select resume file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Resume File",
            "",
            "PDF Files (*.pdf);;All Files (*.*)"
        )
        if file_path:
            self.resume_file_edit.setText(file_path)
    
    def test_connection(self):
        """Test the email connection settings"""
        # Get current settings
        email_address = self.email_edit.text()
        email_password = self.password_edit.text()
        smtp_server = self.smtp_server_edit.text()
        smtp_port = self.smtp_port_spin.value()
        
        # Validate inputs
        if not email_address or not email_password or not smtp_server:
            QMessageBox.warning(self, "Missing Information", "Please fill in all email settings.")
            return
        
        # Create temporary config
        temp_config = {
            'email_address': email_address,
            'email_password': email_password,
            'smtp_server': smtp_server,
            'smtp_port': smtp_port
        }
        
        # Test connection
        try:
            # Disable test button
            self.test_button.setEnabled(False)
            self.test_button.setText("Testing...")
            
            # Create email sender with temporary config
            email_sender = EmailSender(config=temp_config)
            
            # Test connection
            success = email_sender.test_connection()
            
            # Show result
            if success:
                QMessageBox.information(
                    self,
                    "Connection Successful",
                    "Successfully connected to the email server."
                )
            else:
                QMessageBox.critical(
                    self,
                    "Connection Failed",
                    "Failed to connect to the email server. Please check your settings."
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Connection Error",
                f"Error testing connection: {str(e)}"
            )
        finally:
            # Re-enable test button
            self.test_button.setEnabled(True)
            self.test_button.setText("Test Connection")
    
    def save_settings(self):
        """Save the current settings"""
        # Get current settings
        email_address = self.email_edit.text()
        email_password = self.password_edit.text()
        smtp_server = self.smtp_server_edit.text()
        smtp_port = self.smtp_port_spin.value()
        resume_path = self.resume_file_edit.text()
        always_attach_resume = self.attach_resume_check.isChecked()
        delay_between_emails = self.delay_spin.value()
        default_test_mode = self.default_test_mode_check.isChecked()
        
        # Update config
        self.config.update({
            'email_address': email_address,
            'email_password': email_password,
            'smtp_server': smtp_server,
            'smtp_port': smtp_port,
            'resume_path': resume_path,
            'always_attach_resume': always_attach_resume,
            'delay_between_emails': delay_between_emails,
            'default_test_mode': default_test_mode
        })
        
        # Save config
        if self.save_config(self.config):
            QMessageBox.information(
                self,
                "Settings Saved",
                "Settings have been saved successfully."
            )
        else:
            QMessageBox.critical(
                self,
                "Error",
                "Failed to save settings."
            )
